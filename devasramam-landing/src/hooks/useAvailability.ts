import { useState, useEffect } from 'react';

interface AvailabilityData {
  blockedDates: string[];
  lastUpdated: string;
}

interface UseAvailabilityReturn {
  blockedDates: Date[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useAvailability(): UseAvailabilityReturn {
  const [blockedDates, setBlockedDates] = useState<Date[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAvailability = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/availability');

      if (!response.ok) {
        throw new Error(`Failed to fetch availability: ${response.status}`);
      }

      const data: AvailabilityData = await response.json();

      // Convert string dates back to Date objects
      const dates = data.blockedDates.map(dateStr => {
        const date = new Date(dateStr + 'T00:00:00'); // Ensure consistent timezone handling
        return date;
      });
      setBlockedDates(dates);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unable to load availability');
      console.error('Error fetching availability:', err);
      // Set empty array on error so calendar still works
      setBlockedDates([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAvailability();
  }, []);

  return {
    blockedDates,
    isLoading,
    error,
    refetch: fetchAvailability
  };
}
