"use client";

import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import HeroSection from "@/components/sections/HeroSection";
import StorySection from "@/components/sections/StorySection";
import TestimonialsMarquee from "@/components/sections/TestimonialsMarquee";
import GallerySection from "@/components/sections/GallerySection";
import AmenitiesSection from "@/components/sections/AmenitiesSection";
import BookingSection from "@/components/sections/BookingSection";
import FloatingCTA from "@/components/common/FloatingCTA";
import StickyMobileCTA from "@/components/common/StickyMobileCTA";
import ExitIntentModal from "@/components/common/ExitIntentModal";
import Analytics from "@/components/common/Analytics";

export default function Home() {
  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-[#FAF9F6]">
      <Header onNavigate={scrollToSection} />
      <HeroSection onNavigate={scrollToSection} />
      <StorySection onNavigate={scrollToSection} />
      <TestimonialsMarquee />
      <GallerySection onNavigate={scrollToSection} />
      <AmenitiesSection />
      <BookingSection />
      <Footer />

      {/* Conversion Optimization Components */}
      <FloatingCTA onNavigate={scrollToSection} />
      <StickyMobileCTA onNavigate={scrollToSection} />
      <ExitIntentModal onNavigate={scrollToSection} />

      {/* Analytics & Performance Monitoring */}
      <Analytics />
    </div>
  );
}


