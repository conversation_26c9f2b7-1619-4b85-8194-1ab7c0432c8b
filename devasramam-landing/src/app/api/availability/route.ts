import { NextRequest, NextResponse } from 'next/server';
import { BRAND } from '@/lib/constants';

interface CalendarEvent {
  start: Date;
  end: Date;
  summary: string;
}

// IMPORTANT: This is a READ-ONLY endpoint. It NEVER writes data to Airbnb.
// It only fetches and parses the public iCal feed for availability display.
export async function GET(request: NextRequest) {
  try {
    // SAFETY CHECK: Ensure we're only doing GET requests (read-only)
    if (request.method !== 'GET') {
      return NextResponse.json(
        { error: 'Only GET requests allowed - this is a read-only endpoint' },
        { status: 405 }
      );
    }

    // Fetch the iCal data from Airbnb (READ-ONLY operation)
    const response = await fetch(BRAND.airbnbIcalUrl, {
      method: 'GET', // Explicitly specify GET method
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; DevasramamBot/1.0; Read-Only)',
      },
      // Cache for 2 hours to avoid hitting Airbnb too frequently
      next: { revalidate: 7200 } // 2 hours = 7200 seconds
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch calendar: ${response.status}`);
    }

    const icalData = await response.text();
    
    // Parse the iCal data
    const blockedDates = parseICalData(icalData);
    
    return NextResponse.json({ 
      blockedDates: blockedDates.map(date => date.toISOString().split('T')[0]),
      lastUpdated: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error fetching availability:', error);
    return NextResponse.json(
      { error: 'Failed to fetch availability data' },
      { status: 500 }
    );
  }
}

function parseICalData(icalData: string): Date[] {
  const blockedDates: Date[] = [];
  const lines = icalData.split('\n');
  
  let currentEvent: Partial<CalendarEvent> = {};
  let inEvent = false;
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    if (trimmedLine === 'BEGIN:VEVENT') {
      inEvent = true;
      currentEvent = {};
    } else if (trimmedLine === 'END:VEVENT' && inEvent) {
      if (currentEvent.start && currentEvent.end) {
        // Add all dates in the range to blocked dates
        const dates = getDateRange(currentEvent.start, currentEvent.end);
        blockedDates.push(...dates);
      }
      inEvent = false;
    } else if (inEvent) {
      if (trimmedLine.startsWith('DTSTART')) {
        const dateStr = trimmedLine.split(':')[1];
        currentEvent.start = parseICalDate(dateStr);
      } else if (trimmedLine.startsWith('DTEND')) {
        const dateStr = trimmedLine.split(':')[1];
        currentEvent.end = parseICalDate(dateStr);
      } else if (trimmedLine.startsWith('SUMMARY')) {
        currentEvent.summary = trimmedLine.split(':')[1];
      }
    }
  }
  
  return blockedDates;
}

function parseICalDate(dateStr: string): Date {
  // Handle both date-only (YYYYMMDD) and datetime (YYYYMMDDTHHMMSSZ) formats
  if (dateStr.length === 8) {
    // Date only format: YYYYMMDD
    const year = parseInt(dateStr.substring(0, 4));
    const month = parseInt(dateStr.substring(4, 6)) - 1; // Month is 0-indexed
    const day = parseInt(dateStr.substring(6, 8));
    return new Date(year, month, day);
  } else {
    // DateTime format: YYYYMMDDTHHMMSSZ
    const year = parseInt(dateStr.substring(0, 4));
    const month = parseInt(dateStr.substring(4, 6)) - 1;
    const day = parseInt(dateStr.substring(6, 8));
    return new Date(year, month, day);
  }
}

function getDateRange(start: Date, end: Date): Date[] {
  const dates: Date[] = [];
  const currentDate = new Date(start);
  
  // For booking purposes, we want to block all dates from start to end (exclusive of end date)
  while (currentDate < end) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return dates;
}

// Explicitly disable all other HTTP methods to ensure read-only operation
export async function POST() {
  return NextResponse.json(
    { error: 'POST not allowed - this is a read-only endpoint' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'PUT not allowed - this is a read-only endpoint' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'DELETE not allowed - this is a read-only endpoint' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'PATCH not allowed - this is a read-only endpoint' },
    { status: 405 }
  );
}
