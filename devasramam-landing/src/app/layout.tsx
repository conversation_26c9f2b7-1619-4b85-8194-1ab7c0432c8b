import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON>, Lato } from "next/font/google";
import "./globals.css";
import { SEO, BRAND } from "@/lib/constants";
import StructuredData from "@/components/common/StructuredData";

const lora = Lora({
  variable: "--font-lora",
  subsets: ["latin"],
  weight: ["400", "700"],
});

const lato = Lato({
  variable: "--font-lato",
  subsets: ["latin"],
  weight: ["300", "400", "700"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://devasramam.com'),
  title: SEO.title,
  description: SEO.description,
  keywords: SEO.keywords,
  authors: [{ name: BRAND.host }],
  creator: BRAND.host,
  publisher: BRAND.name,
  openGraph: {
    title: SEO.title,
    description: SEO.description,
    url: 'https://devasramam.com',
    siteName: BRAND.name,
    images: [
      {
        url: SEO.ogImage,
        width: 1200,
        height: 630,
        alt: BRAND.tagline,
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: SEO.title,
    description: SEO.description,
    images: [SEO.ogImage],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: "/assets/images/monogram.png",
    shortcut: "/assets/images/monogram.png",
    apple: "/assets/images/monogram.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <StructuredData />
      </head>
      <body
        className={`${lora.variable} ${lato.variable} font-sans antialiased`}
        style={{ fontFamily: 'var(--font-lato)' }}
      >
        {children}
      </body>
    </html>
  );
}
