"use client";

import { useState } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { X, ZoomIn } from "lucide-react";
import { ASSETS } from "@/lib/constants";

interface GallerySectionProps {
  onNavigate?: (sectionId: string) => void;
}

export default function GallerySection({ onNavigate }: GallerySectionProps) {
  const [lightboxImage, setLightboxImage] = useState<string | null>(null);
  const [imageIndex, setImageIndex] = useState(0);

  const openLightbox = (imageSrc: string, index: number) => {
    setLightboxImage(imageSrc);
    setImageIndex(index);
  };

  const closeLightbox = () => {
    setLightboxImage(null);
  };

  const nextImage = () => {
    const nextIndex = (imageIndex + 1) % ASSETS.images.gallery.length;
    setImageIndex(nextIndex);
    setLightboxImage(ASSETS.images.gallery[nextIndex]);
  };

  const prevImage = () => {
    const prevIndex = imageIndex === 0 ? ASSETS.images.gallery.length - 1 : imageIndex - 1;
    setImageIndex(prevIndex);
    setLightboxImage(ASSETS.images.gallery[prevIndex]);
  };

  return (
    <section id="gallery" className="py-24 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-[#333333] mb-4" 
              style={{ fontFamily: 'var(--font-lora)' }}>
            A Living Gallery
          </h2>
          <p className="text-xl text-[#666666] max-w-2xl mx-auto">
            Every corner tells a story. Explore the artistic details and architectural beauty 
            that make Devasramam truly unique.
          </p>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-12">
          {ASSETS.images.gallery.map((image, index) => (
            <div
              key={index}
              className={`relative cursor-pointer overflow-hidden rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-xl group ${
                index === 0 ? 'col-span-2 row-span-2 h-96' :
                index === 3 ? 'col-span-2 h-48' :
                index === 6 ? 'row-span-2 h-96' :
                'h-48'
              }`}
              onClick={() => openLightbox(image, index)}
            >
              <Image
                src={image}
                alt={`Devasramam luxury heritage villa interior and exterior view ${index + 1} - traditional Kerala architecture with modern amenities`}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-110"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-300 flex items-center justify-center">
                <ZoomIn 
                  size={32} 
                  className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" 
                />
              </div>
              
              {/* Featured Badge for first image */}
              {index === 0 && (
                <div className="absolute top-4 left-4 bg-[#004D40] text-white px-3 py-1 rounded-full text-sm font-medium">
                  Featured
                </div>
              )}
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gray-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-[#333333] mb-4" style={{ fontFamily: 'var(--font-lora)' }}>
            Ready to Experience This Beauty?
          </h3>
          <p className="text-[#666666] mb-6 max-w-xl mx-auto">
            Don&apos;t just view the art—live within it. Book your stay and immerse yourself 
            in this architectural masterpiece.
          </p>
          <Button
            size="lg"
            className="bg-[#004D40] hover:bg-[#00695C] text-white px-8 py-3 text-lg rounded-full"
            onClick={() => onNavigate?.('booking')}
          >
            Book Your Stay
          </Button>
        </div>
      </div>

      {/* Lightbox */}
      {lightboxImage && (
        <div
          className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-4"
          onClick={closeLightbox}
        >
          <div className="relative max-w-5xl max-h-full">
            <Image
              src={lightboxImage}
              alt="Devasramam luxury heritage villa detailed view - traditional Kerala architecture and interior design"
              width={1200}
              height={800}
              className="object-contain max-h-[90vh] rounded-lg"
            />
            
            {/* Close Button */}
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 bg-white/10 border-white/20 text-white hover:bg-white/20 rounded-full p-2"
              onClick={closeLightbox}
            >
              <X size={20} />
            </Button>
            
            {/* Navigation Buttons */}
            <Button
              variant="outline"
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/10 border-white/20 text-white hover:bg-white/20 rounded-full p-2"
              onClick={(e) => {
                e.stopPropagation();
                prevImage();
              }}
            >
              ←
            </Button>
            
            <Button
              variant="outline"
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/10 border-white/20 text-white hover:bg-white/20 rounded-full p-2"
              onClick={(e) => {
                e.stopPropagation();
                nextImage();
              }}
            >
              →
            </Button>
            
            {/* Image Counter */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white/10 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm">
              {imageIndex + 1} / {ASSETS.images.gallery.length}
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
