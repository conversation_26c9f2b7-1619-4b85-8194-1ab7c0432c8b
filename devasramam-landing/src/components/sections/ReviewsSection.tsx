import { Star, Award, Quote } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TESTIMONIALS } from "@/lib/constants";
import { LaurelWreath } from "@/components/ui/laurel-wreath";
import { motion } from "framer-motion";

interface ReviewsSectionProps {
  onNavigate?: (sectionId: string) => void;
}

export default function ReviewsSection({ onNavigate }: ReviewsSectionProps) {
  return (
    <section id="reviews" className="py-24 px-4 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center gap-3 mb-6">
            <LaurelWreath size={112} className="text-[#004D40]" />
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-[#333333] mb-4" 
              style={{ fontFamily: 'var(--font-lora)' }}>
            What Our Guests Say
          </h2>
          
          <p className="text-xl text-[#666666] max-w-2xl mx-auto">
            Discover why discerning travelers choose Devasramam for their most memorable stays.
          </p>
        </motion.div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="text-center">
            <div className="text-4xl font-bold text-[#004D40] mb-2">4.9</div>
            <div className="flex justify-center mb-2">
              {[...Array(5)].map((_, i) => (
                <Star key={i} size={20} fill="currentColor" className="text-[#004D40]" />
              ))}
            </div>
            <div className="text-gray-600">Average Rating</div>
          </div>
          
          <div className="text-center">
            <div className="text-4xl font-bold text-[#004D40] mb-2">98%</div>
            <div className="text-gray-600">Would Recommend</div>
          </div>
          
          <div className="text-center">
            <div className="text-4xl font-bold text-[#004D40] mb-2">150+</div>
            <div className="text-gray-600">Happy Guests</div>
          </div>
        </div>

        {/* Testimonials */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {TESTIMONIALS.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card className="p-8 border-none shadow-xl hover:shadow-2xl transition-all duration-300 relative overflow-hidden group hover:scale-105">
              {/* Quote Icon */}
              <div className="absolute top-4 right-4 opacity-10">
                <Quote size={48} className="text-[#004D40]" />
              </div>
              
              {/* Stars */}
              <div className="flex justify-center mb-6">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} size={20} fill="currentColor" className="text-[#004D40]" />
                ))}
              </div>
              
              {/* Review Text */}
              <blockquote className="text-lg text-[#333333] mb-6 italic leading-relaxed text-center">
                &ldquo;{testimonial.text}&rdquo;
              </blockquote>
              
              {/* Author */}
              <div className="text-center">
                <div className="font-semibold text-[#333333]">{testimonial.author}</div>
                <div className="text-sm text-[#666666]">{testimonial.date}</div>
              </div>
            </Card>
            </motion.div>
          ))}
        </div>

        {/* Social Proof */}
        <div className="bg-[#004D40] rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4" style={{ fontFamily: 'var(--font-lora)' }}>
            Join Our Community of Art Lovers
          </h3>
          <p className="text-white/90 mb-6 max-w-2xl mx-auto">
            Experience the perfect blend of artistic heritage and modern comfort. 
            Book now and become part of our story.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              className="bg-white text-[#004D40] hover:bg-gray-100 px-8 py-3 text-lg rounded-full font-semibold"
              onClick={() => onNavigate?.('booking')}
            >
              Book Your Experience
            </Button>
            
            <div className="flex items-center space-x-2 text-white/80">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm">Limited availability this season</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
