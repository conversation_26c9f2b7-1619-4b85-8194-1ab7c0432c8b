"use client";

import Image from "next/image";
import { ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { BRAND, ASSETS } from "@/lib/constants";
import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";

interface HeroSectionProps {
  onNavigate?: (sectionId: string) => void;
}

export default function HeroSection({ onNavigate }: HeroSectionProps) {
  const [hasScrolled, setHasScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setHasScrolled(window.scrollY > 100);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  return (
    <section className="relative h-screen min-h-[600px] flex items-center justify-center overflow-hidden">
      {/* Responsive Background Image */}
      <picture className="absolute inset-0">
        {/* Mobile Image - optimized for vertical composition */}
        <source
          media="(max-width: 768px)"
          srcSet={ASSETS.images.heroMobile || ASSETS.images.hero}
        />
        {/* Desktop Image */}
        <source
          media="(min-width: 769px)"
          srcSet={ASSETS.images.hero}
        />
        {/* Fallback Image */}
        <Image
          src={ASSETS.images.hero}
          alt="Devasramam luxury heritage villa in Kerala showcasing traditional architecture with modern amenities surrounded by lush tropical greenery"
          fill
          className="object-cover"
          priority
        />
      </picture>

      {/* Aesthetic Floating Shapes */}
      <div className="absolute top-20 right-20 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-32 left-16 w-16 h-16 bg-[#8BC34A]/20 transform rotate-45 blur-lg floating-shapes"></div>
      <div className="absolute top-1/3 right-1/4 w-12 h-12 bg-white/15 rounded-full blur-md animate-pulse" style={{animationDelay: '2s'}}></div>

      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-black/30 to-black/50" />
      
      {/* Content */}
      <motion.div
        className="relative z-10 text-center text-white px-4 sm:px-6 max-w-4xl mx-auto"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 1, ease: "easeOut" }}
      >
        {/* Logo */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          <div className="relative w-24 h-24 mx-auto mb-6">
            <Image
              src={ASSETS.logo}
              alt="Devasramam luxury heritage villa logo - architectural masterpiece in Kerala"
              fill
              className="object-contain filter brightness-0 invert"
            />
          </div>
        </motion.div>

        {/* Main Heading */}
        <motion.h1
          className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight"
          style={{ fontFamily: 'var(--font-lora)' }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <div className="mb-2">{BRAND.tagline.primary}</div>
          <div className="text-3xl md:text-4xl lg:text-5xl">{BRAND.tagline.secondary}</div>
        </motion.h1>

        {/* Subheading */}
        <motion.p
          className="text-xl md:text-2xl mb-8 text-white/90 max-w-2xl mx-auto leading-relaxed"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.7 }}
        >
          Experience tranquil intimacy in a living gallery where every detail tells a story of creativity and heritage.
        </motion.p>
        
        {/* CTA Buttons */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.1 }}
        >
          <Button
            size="lg"
            className="bg-[#004D40] hover:bg-[#00695C] text-white px-8 py-4 text-lg font-semibold rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
            onClick={() => onNavigate?.('story')}
          >
            Discover the Story
          </Button>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8">
            <Button
              variant="link"
              className="text-white/80 hover:text-white underline text-lg"
              onClick={() => onNavigate?.('booking')}
            >
              Check Availability
            </Button>
            
            <div className="flex items-center space-x-2 text-white/80">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm">Available for booking</span>
            </div>
          </div>
        </motion.div>

        {/* Conditional Elements based on scroll */}
        <AnimatePresence mode="wait">
          {!hasScrolled ? (
            // Scroll Indicator - shown initially
            <motion.div
              key="scroll-indicator"
              className="mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col items-center space-y-2 cursor-pointer" onClick={() => onNavigate?.('story')}>
                <span className="text-white/80 text-sm">Scroll to explore</span>
                <ChevronDown size={32} className="text-white animate-bounce" />
              </div>
            </motion.div>
          ) : (
            // Urgency Element - shown after scrolling
            <motion.div
              key="urgency-element"
              className="mt-8 inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 text-sm text-white/90"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <span className="w-2 h-2 bg-orange-400 rounded-full mr-2 animate-pulse"></span>
              Only 3 bookings available this month
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </section>
  );
}
