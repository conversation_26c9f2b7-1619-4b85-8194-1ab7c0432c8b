"use client";

import { motion } from "framer-motion";
import { 
  <PERSON>if<PERSON>, 
  <PERSON>, 
  <PERSON>Hat, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ine,
  Home,
  Shirt
} from "lucide-react";

interface AmenityProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function AmenityCard({ icon, title, description }: AmenityProps) {
  return (
    <motion.div
      className="flex flex-col items-center text-center p-4 md:p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -5 }}
    >
      <div className="w-12 h-12 md:w-16 md:h-16 bg-[#004D40]/10 rounded-full flex items-center justify-center mb-3 md:mb-4 group-hover:bg-[#004D40]/20 transition-colors">
        <div className="text-[#004D40] text-sm md:text-base">
          {icon}
        </div>
      </div>
      <h3 className="text-sm md:text-lg font-semibold text-[#333333] mb-1 md:mb-2" style={{ fontFamily: 'var(--font-lora)' }}>
        {title}
      </h3>
      <p className="text-xs md:text-sm text-[#666666] leading-relaxed">
        {description}
      </p>
    </motion.div>
  );
}

const coreExperience = [
  {
    icon: <Home size={24} />,
    title: "Private Garden-View Terrace",
    description: "Your personal space for morning coffee or evening relaxation, overlooking the lush greenery of the sanctuary."
  },
  {
    icon: <TreePine size={24} />,
    title: "Lush Garden Sanctuary",
    description: "Feel free to wander, read, or simply be at peace in the quiet, curated green space surrounding the home."
  },
  {
    icon: <BookOpen size={24} />,
    title: "Curated Reading Library",
    description: "In place of a television, we offer a collection of books on art, culture, and fiction to inspire and entertain."
  }
];

const modernComforts = [
  {
    icon: <Wifi size={24} />,
    title: "High-Speed WiFi (30 Mbps)",
    description: "Verified for seamless streaming and video calls, making it perfect for a peaceful workation or staying connected."
  },
  {
    icon: <Snowflake size={24} />,
    title: "Air-Conditioned Comfort",
    description: "Escape the heat with a modern, quiet AC unit and a ceiling fan to ensure a restful stay."
  },
  {
    icon: <Laptop size={24} />,
    title: "Dedicated Workspace",
    description: "A comfortable desk and chair within your private room, allowing for focused work whenever you need it."
  }
];

const practicalAssured = [
  {
    icon: <Car size={24} />,
    title: "Free & Secure On-Premise Parking",
    description: "Arrive and depart with ease, knowing your vehicle is safely parked just steps away."
  },
  {
    icon: <ChefHat size={24} />,
    title: "Full Kitchen Access",
    description: "Prepare your own meals and snacks in our shared kitchen, equipped with a kettle, dishes, and cutlery."
  },
  {
    icon: <Shirt size={24} />,
    title: "Fully-Stocked Essentials",
    description: "We provide fresh linens, towels, soaps, shampoo, an iron, and everything else you need for a comfortable stay."
  }
];

export default function AmenitiesSection() {
  return (
    <section className="py-24 px-4 bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-[#333333] mb-4" 
              style={{ fontFamily: 'var(--font-lora)' }}>
            Your Sanctuary, Fully Equipped
          </h2>
          <p className="text-xl text-[#666666] max-w-2xl mx-auto">
            Every detail thoughtfully curated to enhance your artistic retreat experience.
          </p>
        </motion.div>

        {/* Core Experience */}
        <div className="mb-12 md:mb-16">
          <motion.h3
            className="text-xl md:text-2xl font-bold text-[#333333] text-center mb-6 md:mb-8"
            style={{ fontFamily: 'var(--font-lora)' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            The Core Experience
          </motion.h3>
          {/* Mobile: 2 columns, Desktop: 3 columns */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-8">
            {coreExperience.map((amenity, index) => (
              <AmenityCard key={index} {...amenity} />
            ))}
          </div>
        </div>

        {/* Modern Comforts */}
        <div className="mb-12 md:mb-16">
          <motion.h3
            className="text-xl md:text-2xl font-bold text-[#333333] text-center mb-6 md:mb-8"
            style={{ fontFamily: 'var(--font-lora)' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Modern Comforts
          </motion.h3>
          {/* Mobile: 2 columns, Desktop: 3 columns */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-8">
            {modernComforts.map((amenity, index) => (
              <AmenityCard key={index} {...amenity} />
            ))}
          </div>
        </div>

        {/* Practical & Assured */}
        <div className="mb-12 md:mb-16">
          <motion.h3
            className="text-xl md:text-2xl font-bold text-[#333333] text-center mb-6 md:mb-8"
            style={{ fontFamily: 'var(--font-lora)' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            Practical & Assured
          </motion.h3>
          {/* Mobile: 2 columns, Desktop: 3 columns */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-8">
            {practicalAssured.map((amenity, index) => (
              <AmenityCard key={index} {...amenity} />
            ))}
          </div>
        </div>

        {/* Honesty Section */}
        <motion.div 
          className="mt-16 p-8 bg-[#004D40]/5 rounded-2xl border border-[#004D40]/10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h3 className="text-xl font-semibold text-[#333333] mb-4 text-center" 
              style={{ fontFamily: 'var(--font-lora)' }}>
            A Note on Your Stay
          </h3>
          <p className="text-[#666666] leading-relaxed text-center max-w-4xl mx-auto">
            To enhance the tranquil and authentic experience of the home, please note a few details. 
            In place of a TV, we encourage you to explore the library and garden. While the room 
            does not have a washing machine or hot water for the shower, we are happy to recommend 
            excellent local laundry services. Our goal is a peaceful stay, connected to the unique 
            character of this artistic home.
          </p>
        </motion.div>
      </div>
    </section>
  );
}
