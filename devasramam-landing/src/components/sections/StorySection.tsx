import Image from "next/image";
import { Button } from "@/components/ui/button";
import { BRAND, ASSETS } from "@/lib/constants";

interface StorySectionProps {
  onNavigate?: (sectionId: string) => void;
}

export default function StorySection({ onNavigate }: StorySectionProps) {
  return (
    <section id="story" className="relative py-24 px-4 bg-gradient-to-br from-white via-[#FAF9F6] to-[#004D40]/5 overflow-hidden">
      {/* Organic Background Shapes */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-[#004D40]/5 rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-[#8BC34A]/10 rounded-full blur-2xl transform -translate-x-1/2 translate-y-1/2"></div>

      <div className="max-w-7xl mx-auto relative">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Image with Overlapping Elements */}
          <div className="relative h-96 lg:h-[600px] order-2 lg:order-1">
            {/* Main Image */}
            <div className="relative h-full w-full">
              <Image
                src={ASSETS.images.story}
                alt="Devasramam Interior"
                fill
                className="object-cover rounded-2xl shadow-2xl"
              />
              {/* Overlapping Decorative Element */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-[#004D40] rounded-full opacity-20 blur-sm"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-[#8BC34A] rounded-full opacity-15 blur-md"></div>
            </div>

            {/* Floating Badge with Enhanced Design */}
            <div className="absolute top-6 left-6 bg-gradient-to-r from-[#004D40] to-[#00695C] text-white px-6 py-3 rounded-full text-sm font-medium shadow-lg backdrop-blur-sm border border-white/20">
              <span className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></span>
                Architect-Designed
              </span>
            </div>

            {/* Overlapping Stats Card */}
            <div className="absolute -bottom-8 -right-8 bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-gray-100 transform rotate-3">
              <div className="text-center">
                <div className="text-2xl font-bold text-[#004D40]" style={{ fontFamily: 'var(--font-lora)' }}>4.8★</div>
                <div className="text-sm text-gray-600">Guest Rating</div>
              </div>
            </div>
          </div>

          {/* Content with Enhanced Visual Hierarchy */}
          <div className="relative space-y-8 order-1 lg:order-2">
            {/* Decorative Background Element */}
            <div className="absolute -top-12 -left-12 w-24 h-24 bg-gradient-to-br from-[#004D40]/10 to-[#8BC34A]/10 rounded-full blur-xl"></div>

            {/* Enhanced Badge */}
            <div className="inline-flex items-center bg-gradient-to-r from-[#004D40]/10 to-[#8BC34A]/10 text-[#004D40] px-6 py-3 rounded-full text-sm font-medium border border-[#004D40]/20 backdrop-blur-sm">
              <span className="w-3 h-3 bg-gradient-to-r from-[#004D40] to-[#8BC34A] rounded-full mr-3 animate-pulse"></span>
              Heritage & Art
            </div>

            {/* Enhanced Heading with Gradient Text */}
            <div className="relative">
              <h2 className="text-4xl lg:text-6xl font-bold leading-tight"
                  style={{ fontFamily: 'var(--font-lora)' }}>
                <span className="text-[#333333]">Designed with Soul,</span><br />
                <span className="bg-gradient-to-r from-[#004D40] via-[#00695C] to-[#26A69A] bg-clip-text text-transparent">
                  Inherited with Love.
                </span>
              </h2>
              {/* Decorative underline */}
              <div className="absolute -bottom-2 left-0 w-24 h-1 bg-gradient-to-r from-[#004D40] to-[#8BC34A] rounded-full"></div>
            </div>
            
            {/* Story Content */}
            <div className="space-y-6 text-lg text-[#333333] leading-relaxed">
              <p className="text-xl font-medium text-[#004D40]">
                I am {BRAND.host}. I&apos;m not only your host but also the architect of this house.
              </p>
              
              <p>
                This home is a personal tribute to the artistic philosophy of my father, 
                the legendary M.V. Devan. This isn&apos;t just a place to stay; it&apos;s a 
                living gallery where every brick, every beam, and every brushstroke tells 
                a story of creativity, heritage, and the profound connection between art and life.
              </p>
              
              <p>
                Here, you&apos;ll find yourself surrounded by original artworks, thoughtful design, 
                and the kind of tranquil beauty that inspires reflection and renewal. This is 
                more than accommodation—it&apos;s an invitation to step into a world where 
                architecture and art dance together in perfect harmony.
              </p>
            </div>

            {/* Enhanced Features with Overlapping Cards */}
            <div className="relative py-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="relative text-center p-6 bg-gradient-to-br from-white to-[#004D40]/5 rounded-2xl shadow-lg border border-[#004D40]/10 transform hover:scale-105 transition-transform duration-300">
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-[#8BC34A] rounded-full opacity-20"></div>
                  <div className="text-3xl font-bold bg-gradient-to-r from-[#004D40] to-[#00695C] bg-clip-text text-transparent" style={{ fontFamily: 'var(--font-lora)' }}>50+</div>
                  <div className="text-sm text-gray-600 font-medium">Original Artworks</div>
                </div>
                <div className="relative text-center p-6 bg-gradient-to-br from-white to-[#8BC34A]/5 rounded-2xl shadow-lg border border-[#8BC34A]/10 transform hover:scale-105 transition-transform duration-300 mt-4">
                  <div className="absolute -top-2 -left-2 w-6 h-6 bg-[#004D40] rounded-full opacity-20"></div>
                  <div className="text-3xl font-bold bg-gradient-to-r from-[#8BC34A] to-[#004D40] bg-clip-text text-transparent" style={{ fontFamily: 'var(--font-lora)' }}>1</div>
                  <div className="text-sm text-gray-600 font-medium">Architect Host</div>
                </div>
              </div>
            </div>

            {/* CTA */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="bg-[#004D40] hover:bg-[#00695C] text-white px-8 py-3 text-lg rounded-full"
                onClick={() => onNavigate?.('booking')}
              >
                Book Your Stay
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-[#004D40] text-[#004D40] hover:bg-[#004D40] hover:text-white px-8 py-3 text-lg rounded-full"
                onClick={() => onNavigate?.('gallery')}
              >
                View Gallery
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
