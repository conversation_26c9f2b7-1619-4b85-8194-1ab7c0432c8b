import Image from "next/image";
import { MapPin, Phone, Mail } from "lucide-react";
import { BRAND, ASSETS } from "@/lib/constants";

export default function Footer() {
  return (
    <footer className="bg-[#004D40] text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-center md:justify-start">
              <Image
                src={ASSETS.logo}
                alt={`${BRAND.name} - An Architect's Retreat`}
                width={600}
                height={220}
                className="object-contain filter brightness-0 invert h-32 w-auto"
              />
            </div>
            <p className="text-white/80 text-sm leading-relaxed">
              A personal tribute to the artistic philosophy of legendary <PERSON><PERSON><PERSON><PERSON>. 
              Designed with soul, inherited with love.
            </p>
          </div>

          {/* Contact Section */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Get in Touch</h4>
            <div className="space-y-3 text-sm text-white/80">
              <div className="flex items-center space-x-2">
                <Phone size={16} />
                <span>+{BRAND.whatsapp}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail size={16} />
                <span>{BRAND.email}</span>
              </div>
              <div className="flex items-start space-x-2">
                <MapPin size={16} className="mt-0.5 flex-shrink-0" />
                <div>
                  <p>Aluva, Ernakulam, Kerala</p>
                  <a
                    href={BRAND.googleMapsUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/60 hover:text-white transition-colors underline text-xs mt-1 inline-block"
                  >
                    View on Google Maps
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Legacy Section */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">The Legacy</h4>
            <p className="text-sm text-white/80 leading-relaxed">
              Honoring the artistic vision of M.V. Devan through thoughtful architecture 
              and curated experiences that celebrate the intersection of art and life.
            </p>
          </div>
        </div>

        <div className="border-t border-white/20 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-white/60">
            © {new Date().getFullYear()} {BRAND.name}. All rights reserved.
          </p>
          <p className="text-sm text-white/60 mt-2 md:mt-0">
            Crafted with care for discerning guests
          </p>
        </div>
      </div>
    </footer>
  );
}
