"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { BRAND, ASSETS, NAVIGATION } from "@/lib/constants";

interface HeaderProps {
  onNavigate?: (sectionId: string) => void;
}

export default function Header({ onNavigate }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleNavClick = (href: string) => {
    if (href.startsWith("#")) {
      const sectionId = href.substring(1);
      onNavigate?.(sectionId);
      setIsMobileMenuOpen(false);
    }
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-white/95 backdrop-blur-md shadow-lg"
          : "bg-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Image
              src={ASSETS.logo}
              alt={`${BRAND.name} - An Architect's Retreat`}
              width={2400}
              height={880}
              className="object-contain h-16 lg:h-20 w-auto"
              priority
            />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {NAVIGATION.map((item) => (
              <button
                key={item.label}
                onClick={() => handleNavClick(item.href)}
                className={`text-sm font-medium transition-colors hover:opacity-80 ${
                  'isPrimary' in item && item.isPrimary
                    ? `px-6 py-2 rounded-full ${
                        isScrolled
                          ? "bg-[#004D40] text-white hover:bg-[#004D40]/90"
                          : "bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm"
                      }`
                    : isScrolled
                    ? "text-gray-700 hover:text-[#004D40]"
                    : "text-white hover:text-white/80"
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden p-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle menu"
          >
            <div className="w-6 h-6 flex flex-col justify-center items-center">
              <span
                className={`block w-5 h-0.5 transition-all duration-300 ${
                  isScrolled ? "bg-gray-900" : "bg-white"
                } ${
                  isMobileMenuOpen ? "rotate-45 translate-y-1" : ""
                }`}
              />
              <span
                className={`block w-5 h-0.5 mt-1 transition-all duration-300 ${
                  isScrolled ? "bg-gray-900" : "bg-white"
                } ${
                  isMobileMenuOpen ? "opacity-0" : ""
                }`}
              />
              <span
                className={`block w-5 h-0.5 mt-1 transition-all duration-300 ${
                  isScrolled ? "bg-gray-900" : "bg-white"
                } ${
                  isMobileMenuOpen ? "-rotate-45 -translate-y-1" : ""
                }`}
              />
            </div>
          </button>
        </div>

        {/* Mobile Menu Overlay */}
        <div className={`fixed inset-0 z-50 lg:hidden transition-all duration-300 ease-in-out ${
          isMobileMenuOpen
            ? 'opacity-100 pointer-events-auto'
            : 'opacity-0 pointer-events-none'
        }`}>
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsMobileMenuOpen(false)}
          />

          {/* Menu Panel */}
          <div className={`absolute top-0 right-0 bottom-0 w-80 max-w-[85vw] bg-[#004D40] transform transition-transform duration-300 ease-in-out ${
            isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
          }`}>
            <div className="flex flex-col h-full">
              {/* Header with Logo and Close Button */}
              <div className="flex items-center justify-between p-6 border-b border-white/20">
                <div className="flex items-center">
                  <Image
                    src={ASSETS.logo}
                    alt={`${BRAND.name} - An Architect's Retreat`}
                    width={120}
                    height={44}
                    className="object-contain filter brightness-0 invert h-8 w-auto"
                  />
                </div>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="p-2 text-white hover:bg-white/10 rounded-lg transition-colors"
                  aria-label="Close menu"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Navigation */}
              <nav className="flex-1 py-8 px-6 space-y-2 overflow-y-auto">
                {NAVIGATION.map((item, index) => (
                  <button
                    key={item.label}
                    onClick={() => handleNavClick(item.href)}
                    className={`block w-full text-left transition-all duration-200 rounded-xl px-4 py-4 text-lg font-medium ${
                      'isPrimary' in item && item.isPrimary
                        ? "bg-white text-[#004D40] hover:bg-white/90 font-semibold shadow-lg"
                        : "text-white hover:bg-white/10"
                    }`}
                    style={{
                      animationDelay: `${index * 100}ms`,
                      animation: isMobileMenuOpen ? 'slideInRight 0.4s ease-out forwards' : 'none'
                    }}
                  >
                    {item.label}
                  </button>
                ))}
              </nav>

              {/* Contact Info Footer */}
              <div className="p-6 border-t border-white/20 bg-white/5">
                <div className="space-y-4">
                  <h4 className="text-white font-semibold text-lg">Get in Touch</h4>
                  <div className="space-y-3">
                    <a
                      href={`https://wa.me/${BRAND.whatsapp}`}
                      className="flex items-center space-x-3 text-white/90 hover:text-white transition-colors"
                    >
                      <span className="text-xl">📱</span>
                      <span className="text-sm">WhatsApp: +{BRAND.whatsapp}</span>
                    </a>
                    <a
                      href={`mailto:${BRAND.email}`}
                      className="flex items-center space-x-3 text-white/90 hover:text-white transition-colors"
                    >
                      <span className="text-xl">✉️</span>
                      <span className="text-sm">{BRAND.email}</span>
                    </a>
                    <a
                      href={BRAND.googleMapsUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-3 text-white/90 hover:text-white transition-colors"
                    >
                      <span className="text-xl">📍</span>
                      <span className="text-sm">Aluva, Kerala</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
