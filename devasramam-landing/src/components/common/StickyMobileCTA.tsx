"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import { BRAND } from "@/lib/constants";
import WhatsAppIcon from "@/components/ui/WhatsAppIcon";

interface StickyMobileCTAProps {
  onNavigate?: (sectionId: string) => void;
}

export default function StickyMobileCTA({ onNavigate }: StickyMobileCTAProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      
      // Show after scrolling past hero section
      setIsVisible(scrollPosition > windowHeight * 0.8);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleQuickBooking = () => {
    const message = `Hi! I'm interested in booking Devasramam. Could you please share availability?`;
    window.open(`https://wa.me/${BRAND.whatsapp}?text=${encodeURIComponent(message)}`, '_blank');
  };

  if (!isVisible) return null;

  return (
    <div className="lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-premium p-4">
      <div className="flex space-x-3">
        <Button
          className="flex-1 bg-[#004D40] hover:bg-[#00695C] text-white rounded-xl py-3"
          onClick={() => onNavigate?.('booking')}
        >
          <Calendar size={18} className="mr-2" />
          Book Now
        </Button>
        
        <Button
          variant="outline"
          className="flex-1 border-[#004D40] text-[#004D40] hover:bg-[#004D40] hover:text-white rounded-xl py-3"
          onClick={handleQuickBooking}
        >
          <WhatsAppIcon size={18} className="mr-2" />
          WhatsApp
        </Button>
      </div>
      
      {/* Urgency indicator */}
      <div className="text-center mt-2">
        <span className="text-xs text-orange-600 font-medium">
          🔥 Only 3 bookings left this month
        </span>
      </div>
    </div>
  );
}
