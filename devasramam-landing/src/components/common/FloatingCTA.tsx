"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { X, Calendar } from "lucide-react";
import { BRAND } from "@/lib/constants";
import WhatsAppIcon from "@/components/ui/WhatsAppIcon";

interface FloatingCTAProps {
  onNavigate?: (sectionId: string) => void;
}

export default function FloatingCTA({ onNavigate }: FloatingCTAProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      
      // Show after scrolling 50% of viewport height
      if (scrollPosition > windowHeight * 0.5 && !isDismissed) {
        setIsVisible(true);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [isDismissed]);

  const handleQuickBooking = () => {
    const message = `Hi! I'm interested in booking Devasramam. Could you please share availability and help me plan my artistic retreat?`;
    window.open(`https://wa.me/${BRAND.whatsapp}?text=${encodeURIComponent(message)}`, '_blank');
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    setIsVisible(false);
  };

  if (!isVisible || isDismissed) return null;

  return (
    <div className="fixed bottom-20 right-4 md:bottom-6 md:right-6 z-40 animate-fade-in-scale">
      <div className="bg-white rounded-xl shadow-premium-lg border border-gray-200 p-3 max-w-[280px] md:max-w-[300px]">
        {/* Close Button */}
        <button
          onClick={handleDismiss}
          className="absolute -top-1 -right-1 w-5 h-5 bg-gray-500 text-white rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors"
          aria-label="Close"
        >
          <X size={10} />
        </button>

        {/* Content */}
        <div className="space-y-2">
          {/* Urgency Badge */}
          <div className="flex items-center space-x-1.5">
            <div className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-pulse"></div>
            <span className="text-xs font-medium text-orange-600">
              Only 3 bookings left
            </span>
          </div>

          {/* Heading */}
          <h3 className="text-sm font-bold text-gray-900" style={{ fontFamily: 'var(--font-lora)' }}>
            Book Your Architectural Retreat
          </h3>

          {/* Single CTA Button */}
          <Button
            size="sm"
            className="w-full bg-[#004D40] hover:bg-[#00695C] text-white rounded-lg py-2 text-xs"
            onClick={handleQuickBooking}
          >
            <WhatsAppIcon size={14} className="mr-1.5" />
            Book on WhatsApp
          </Button>

          {/* Compact Trust Signal */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-xs text-gray-500">
              <span>⭐ {BRAND.stats.rating}</span>
              <span>•</span>
              <span>{BRAND.stats.totalGuests}+ guests</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
