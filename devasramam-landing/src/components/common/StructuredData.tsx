import { BRAND, ASSETS, TESTIMONIALS, PRICING } from "@/lib/constants";

export default function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "LodgingBusiness",
        "@id": "https://devasramam.com/#business",
        "name": BRAND.name,
        "description": BRAND.description,
        "url": "https://devasramam.com",
        "telephone": `+${BRAND.whatsapp}`,
        "priceRange": `${PRICING.currency}${PRICING.basePrice}`,
        "image": [
          `https://devasramam.com${ASSETS.images.hero}`,
          `https://devasramam.com${ASSETS.images.story}`,
          ...ASSETS.images.gallery.slice(0, 5).map(img => `https://devasramam.com${img}`)
        ],
        "logo": `https://devasramam.com${ASSETS.logo}`,
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "IN",
          "addressRegion": "Kerala"
        },
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": "10.8505",
          "longitude": "76.2711"
        },
        "amenityFeature": [
          {
            "@type": "LocationFeatureSpecification",
            "name": "Art Gallery",
            "value": true
          },
          {
            "@type": "LocationFeatureSpecification", 
            "name": "Architect Designed",
            "value": true
          },
          {
            "@type": "LocationFeatureSpecification",
            "name": "Heritage Property",
            "value": true
          }
        ],
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": "4.9",
          "reviewCount": "150",
          "bestRating": "5",
          "worstRating": "1"
        },
        "review": TESTIMONIALS.map(testimonial => ({
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": testimonial.author
          },
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": testimonial.rating,
            "bestRating": "5"
          },
          "reviewBody": testimonial.text,
          "datePublished": testimonial.date
        })),
        "offers": {
          "@type": "Offer",
          "price": PRICING.basePrice,
          "priceCurrency": "INR",
          "availability": "https://schema.org/InStock",
          "validFrom": new Date().toISOString(),
          "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
        }
      },
      {
        "@type": "Person",
        "@id": "https://devasramam.com/#host",
        "name": BRAND.host,
        "jobTitle": "Architect & Host",
        "description": "Architect and host of Devasramam, daughter of legendary artist M.V. Devan",
        "worksFor": {
          "@id": "https://devasramam.com/#business"
        }
      },
      {
        "@type": "WebSite",
        "@id": "https://devasramam.com/#website",
        "url": "https://devasramam.com",
        "name": BRAND.name,
        "description": BRAND.description,
        "publisher": {
          "@id": "https://devasramam.com/#business"
        },
        "potentialAction": {
          "@type": "SearchAction",
          "target": {
            "@type": "EntryPoint",
            "urlTemplate": "https://devasramam.com/?q={search_term_string}"
          },
          "query-input": "required name=search_term_string"
        }
      },
      {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://devasramam.com"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Story",
            "item": "https://devasramam.com#story"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": "Gallery",
            "item": "https://devasramam.com#gallery"
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": "Booking",
            "item": "https://devasramam.com#booking"
          }
        ]
      }
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
