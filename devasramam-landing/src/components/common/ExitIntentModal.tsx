"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { X, Star } from "lucide-react";
import { BRAND, ASSETS } from "@/lib/constants";
import WhatsAppIcon from "@/components/ui/WhatsAppIcon";

interface ExitIntentModalProps {
  onNavigate?: (sectionId: string) => void;
}

export default function ExitIntentModal({ onNavigate }: ExitIntentModalProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasShown, setHasShown] = useState(false);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleMouseLeave = (e: MouseEvent) => {
      // Only trigger if mouse leaves from the top of the page
      if (e.clientY <= 0 && !hasShown) {
        setIsVisible(true);
        setHasShown(true);
      }
    };

    const handleScroll = () => {
      // Also show after 60 seconds of browsing if not shown yet
      if (!hasShown) {
        timeoutId = setTimeout(() => {
          setIsVisible(true);
          setHasShown(true);
        }, 60000);
      }
    };

    // Only add listeners on desktop - check window after component mounts
    const checkAndAddListeners = () => {
      if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
        document.addEventListener("mouseleave", handleMouseLeave);
        document.addEventListener("scroll", handleScroll, { once: true });
      }
    };

    // Use setTimeout to ensure this runs after hydration
    const timeoutCheck = setTimeout(checkAndAddListeners, 0);

    return () => {
      clearTimeout(timeoutCheck);
      document.removeEventListener("mouseleave", handleMouseLeave);
      document.removeEventListener("scroll", handleScroll);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [hasShown]);

  const handleClose = () => {
    setIsVisible(false);
  };

  const handleSpecialOffer = () => {
    const message = `Hi! I saw the special offer on your website. I'm interested in booking Devasramam with the exclusive discount. Could you please share more details?`;
    window.open(`https://wa.me/${BRAND.whatsapp}?text=${encodeURIComponent(message)}`, '_blank');
    handleClose();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleBookNow = () => {
    onNavigate?.('booking');
    handleClose();
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4" onClick={handleBackdropClick}>
      <div className="bg-white rounded-2xl max-w-sm w-full shadow-premium-lg animate-fade-in-scale" onClick={(e) => e.stopPropagation()}>
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors"
          aria-label="Close"
        >
          <X size={16} />
        </button>

        {/* Content */}
        <div className="p-6">
          {/* Logo */}
          <div className="flex justify-center mb-4">
            <Image
              src={ASSETS.logo}
              alt={`${BRAND.name} - An Architect's Retreat`}
              width={160}
              height={55}
              className="object-contain"
            />
          </div>

          {/* Heading */}
          <h2 className="text-xl font-bold text-center text-gray-900 mb-2"
              style={{ fontFamily: 'var(--font-lora)' }}>
            Wait! Don&apos;t Miss Out
          </h2>

          {/* Subheading */}
          <p className="text-center text-gray-600 mb-4 text-sm">
            Get an exclusive 10% discount on your first stay at this architectural masterpiece.
          </p>

          {/* Special Offer Badge */}
          <div className="bg-gradient-to-r from-[#004D40] to-[#00695C] text-white text-center py-2 px-4 rounded-lg mb-4">
            <div className="text-base font-bold">FIRST-TIME GUEST SPECIAL</div>
            <div className="text-xs opacity-90">Save ₹100 on your booking</div>
          </div>

          {/* Features */}
          <div className="space-y-1.5 mb-4">
            <div className="flex items-center text-sm text-gray-700">
              <Star size={16} className="mr-2 text-[#004D40]" />
              <span>10+ original artworks</span>
            </div>
            <div className="flex items-center text-sm text-gray-700">
              <Star size={16} className="mr-2 text-[#004D40]" />
              <span>Architect-designed spaces</span>
            </div>
            <div className="flex items-center text-sm text-gray-700">
              <Star size={16} className="mr-2 text-[#004D40]" />
              <span>Personal host experience</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="space-y-3">
            <Button
              size="lg"
              className="w-full bg-[#004D40] hover:bg-[#00695C] text-white rounded-xl py-3"
              onClick={handleSpecialOffer}
            >
              <WhatsAppIcon size={18} className="mr-2" />
              Claim Special Offer
            </Button>
            
            <Button
              variant="outline"
              size="lg"
              className="w-full border-[#004D40] text-[#004D40] hover:bg-[#004D40] hover:text-white rounded-xl py-3"
              onClick={handleBookNow}
            >
              View Availability
            </Button>
          </div>

          {/* Urgency */}
          <div className="text-center mt-4">
            <span className="text-xs text-orange-600 font-medium">
              ⏰ Offer expires in 24 hours
            </span>
          </div>

          {/* Trust Signal */}
          <div className="text-center mt-4 pt-4 border-t border-gray-100">
            <div className="flex items-center justify-center space-x-1 text-xs text-gray-500">
              <span>⭐ {BRAND.stats.rating} rating</span>
              <span>•</span>
              <span>{BRAND.stats.totalGuests}+ happy guests</span>
              <span>•</span>
              <span>Instant confirmation</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
