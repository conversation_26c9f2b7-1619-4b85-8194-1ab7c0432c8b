"use client";

import { useState, useEffect } from "react";
import { X, ChevronLeft, ChevronRight, Star } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { ALL_TESTIMONIALS } from "@/lib/all-testimonials";

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialReviewIndex?: number;
}

export default function ReviewModal({ isOpen, onClose, initialReviewIndex = 0 }: ReviewModalProps) {
  const [currentIndex, setCurrentIndex] = useState(initialReviewIndex);

  useEffect(() => {
    setCurrentIndex(initialReviewIndex);
  }, [initialReviewIndex]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? ALL_TESTIMONIALS.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === ALL_TESTIMONIALS.length - 1 ? 0 : prev + 1));
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'ArrowLeft') {
      handlePrevious();
    } else if (e.key === 'ArrowRight') {
      handleNext();
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const currentReview = ALL_TESTIMONIALS[currentIndex];

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-full bg-[#004D40] text-white flex items-center justify-center font-semibold">
                {currentReview.author.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)}
              </div>
              <div>
                <h3 className="font-semibold text-lg text-[#333333]">{currentReview.author}</h3>
                <p className="text-sm text-[#666666]">{currentReview.date}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors"
              aria-label="Close modal"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Stars */}
            <div className="flex items-center space-x-1 mb-4">
              {[...Array(currentReview.rating)].map((_, i) => (
                <Star key={i} size={20} className="fill-yellow-400 text-yellow-400" />
              ))}
            </div>

            {/* Review Text */}
            <blockquote className="text-lg text-[#333333] leading-relaxed mb-6">
              "{currentReview.text}"
            </blockquote>

            {/* Navigation */}
            <div className="flex items-center justify-between">
              <button
                onClick={handlePrevious}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ChevronLeft size={20} />
                <span>Previous</span>
              </button>

              <div className="flex items-center space-x-2">
                <span className="text-sm text-[#666666]">
                  {currentIndex + 1} of {ALL_TESTIMONIALS.length}
                </span>
              </div>

              <button
                onClick={handleNext}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <span>Next</span>
                <ChevronRight size={20} />
              </button>
            </div>
          </div>

          {/* Footer */}
          <div className="px-6 pb-6">
            <div className="bg-[#004D40]/5 rounded-lg p-4 text-center">
              <p className="text-sm text-[#666666]">
                Verified guest review from Airbnb
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
