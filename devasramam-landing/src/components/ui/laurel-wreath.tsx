import React from 'react';

interface LaurelWreathProps {
  size?: number;
  className?: string;
}

export const LaurelWreath: React.FC<LaurelWreathProps> = ({ size = 48, className = "" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Left laurel branch */}
      <path
        d="M8 24C8 20 10 16 14 14C16 13 18 14 19 16C20 18 19 20 17 21C15 22 13 21 12 19C11 17 12 15 14 15"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />
      <path
        d="M12 19C10 17 8 15 6 12C5 10 6 8 8 8C10 8 12 10 13 12C14 14 13 16 11 17"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />
      <path
        d="M17 21C15 19 13 17 11 14C10 12 11 10 13 10C15 10 17 12 18 14C19 16 18 18 16 19"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />
      <path
        d="M14 15C12 13 10 11 8 8C7 6 8 4 10 4C12 4 14 6 15 8C16 10 15 12 13 13"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />

      {/* Right laurel branch */}
      <path
        d="M40 24C40 20 38 16 34 14C32 13 30 14 29 16C28 18 29 20 31 21C33 22 35 21 36 19C37 17 36 15 34 15"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />
      <path
        d="M36 19C38 17 40 15 42 12C43 10 42 8 40 8C38 8 36 10 35 12C34 14 35 16 37 17"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />
      <path
        d="M31 21C33 19 35 17 37 14C38 12 37 10 35 10C33 10 31 12 30 14C29 16 30 18 32 19"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />
      <path
        d="M34 15C36 13 38 11 40 8C41 6 40 4 38 4C36 4 34 6 33 8C32 10 33 12 35 13"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />

      {/* Bottom connecting elements */}
      <path
        d="M19 16C20 18 22 20 24 22C26 20 28 18 29 16"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        fill="none"
      />
      
      {/* Center star/crown element */}
      <path
        d="M24 12L25.5 16.5L30 16.5L26.5 19.5L28 24L24 21L20 24L21.5 19.5L18 16.5L22.5 16.5L24 12Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.5"
      />
    </svg>
  );
};
