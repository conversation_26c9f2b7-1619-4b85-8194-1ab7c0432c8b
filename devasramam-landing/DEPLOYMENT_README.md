# Devasramam Landing - Deployment Guide

## 🚀 Quick Deployment Commands

### Prerequisites
```bash
# Ensure you're in the correct directory
cd devasramam-landing

# Install dependencies (if needed)
npm install
```

### Standard Deployment (Recommended)
```bash
# Build and deploy in one command
npm run deploy
```

### Step-by-Step Deployment
```bash
# 1. Build the Next.js application
npm run build

# 2. Build OpenNext for Cloudflare
npx opennextjs-cloudflare build

# 3. Deploy to Cloudflare Workers
npx opennextjs-cloudflare deploy
```

### Development Commands
```bash
# Start development server
npm run dev

# Preview build locally
npm run preview

# Lint code
npm run lint
```

## 📊 Monitoring & Management

### Check Deployment Status
```bash
# List recent deployments
wrangler deployments list

# View current deployment details
wrangler deployment view [DEPLOYMENT_ID]
```

### Worker Management
```bash
# View worker logs (real-time)
wrangler tail

# View worker analytics
wrangler analytics

# Check worker status
wrangler status
```

### Environment Management
```bash
# Generate TypeScript types for Cloudflare env
npm run cf-typegen

# View current environment variables
wrangler secret list
```

## 🔧 Configuration Files

### Key Files
- `wrangler.jsonc` - Cloudflare Worker configuration
- `next.config.ts` - Next.js configuration with OpenNext
- `open-next.config.ts` - OpenNext specific settings
- `package.json` - Dependencies and scripts

### Important Settings
- **Compatibility Date**: `2025-08-01`
- **Node.js Compatibility**: Enabled
- **Assets Binding**: `ASSETS`
- **Observability**: Enabled

## 🎯 Performance Optimizations Applied

### ✅ Safe Optimizations Implemented
1. **API Caching Fixed**: Removed `cache: 'no-cache'` from availability hook
2. **Date Calculations Optimized**: Memoized blocked dates as Set for O(1) lookup
3. **Google Analytics Added**: Integrated G-VQ1YLC6S1N tracking
4. **Memory Leak Prevention**: Proper cleanup in useEffect hooks

### 📈 Expected Performance Improvements
- **Faster API Responses**: Better caching utilization
- **Reduced CPU Usage**: Optimized date calculations
- **Better Analytics**: Google Analytics integration
- **Stable Worker**: Reduced memory leaks

## 🚨 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear Next.js cache
rm -rf .next

# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Try building again
npm run build
```

#### Deployment Failures
```bash
# Check Cloudflare authentication
wrangler whoami

# Re-authenticate if needed
wrangler login

# Try deploying again
npm run deploy
```

#### Worker Issues
```bash
# View real-time logs
wrangler tail

# Check worker status
wrangler status

# Rollback to previous version if needed
wrangler rollback [DEPLOYMENT_ID]
```

### Performance Issues
```bash
# Monitor worker performance
wrangler analytics

# Check Core Web Vitals in browser console
# Analytics component logs performance metrics
```

## 📱 Testing Deployment

### Verify Deployment
1. **Check URL**: https://devasramam-landing.tallyup-invoices.workers.dev
2. **Test Booking Calendar**: Ensure availability loads
3. **Check Analytics**: Verify Google Analytics tracking
4. **Mobile Responsiveness**: Test on different devices

### Performance Checks
- **Page Load Speed**: Should be under 3 seconds
- **Calendar Loading**: Availability should load quickly
- **Scroll Performance**: Smooth scrolling without lag
- **Memory Usage**: No memory leaks in browser dev tools

## 🔄 Rollback Procedure

### Emergency Rollback
```bash
# List recent deployments
wrangler deployments list

# Rollback to previous version
wrangler rollback [PREVIOUS_DEPLOYMENT_ID]
```

### Planned Rollback
```bash
# Deploy specific version
git checkout [PREVIOUS_COMMIT]
npm run deploy
```

## 📞 Support Information

### Contact Details
- **Phone**: 7774008156
- **Email**: <EMAIL>
- **Google Maps**: https://maps.app.goo.gl/gBPHMvuR6Kb9dj9j8

### Analytics
- **Google Analytics ID**: G-VQ1YLC6S1N
- **Guest Count**: 65
- **Rating**: 4.8
- **Pricing**: ₹1200 (1 guest), ₹1700 (2 guests)

## 🔍 Advanced Commands

### Custom Domain Setup
```bash
# Add custom domain (if needed)
wrangler custom-domains add devasramam.com

# List custom domains
wrangler custom-domains list
```

### Environment Variables
```bash
# Set production environment variable
wrangler secret put VARIABLE_NAME

# Delete environment variable
wrangler secret delete VARIABLE_NAME
```

### Worker Scaling
```bash
# View worker usage
wrangler analytics

# Check worker limits
wrangler limits
```

### Debugging
```bash
# Enable debug mode
wrangler tail --debug

# View specific log level
wrangler tail --level error
```

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] Code tested locally (`npm run dev`)
- [ ] Build successful (`npm run build`)
- [ ] No TypeScript errors
- [ ] All dependencies updated

### Post-Deployment
- [ ] Site loads correctly
- [ ] Booking calendar works
- [ ] Google Analytics tracking active
- [ ] Mobile responsiveness verified
- [ ] Performance metrics acceptable

### Emergency Contacts
- **Developer**: Available via this AI assistant
- **Hosting**: Cloudflare Workers (self-service)
- **Domain**: Check domain registrar if needed

---

## 🎉 Latest Deployment

**Version**: 829b80f8-2dc1-4468-be77-1982f1bb2dce
**Date**: January 2025
**Status**: ✅ Successfully Deployed
**Optimizations**: Safe performance improvements applied
**URL**: https://devasramam-landing.tallyup-invoices.workers.dev

### Recent Changes
- ✅ Fixed API caching for better performance
- ✅ Optimized date calculations in booking calendar
- ✅ Added Google Analytics (G-VQ1YLC6S1N)
- ✅ Improved memory management
- ✅ Zero UI/UX impact - all backend optimizations
